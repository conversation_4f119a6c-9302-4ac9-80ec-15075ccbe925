import { IPageRequest } from "../../basic";

export class IPayment<PERSON>romFilter extends IPageRequest {
    begin?:string
    end?:string
    startTime?:string
    endTime?:string
    serviceProviderName?:string
    status?:number
}


export class IPaymentFrom {
    id?: number | null
    paymentCode?: string // 付款单号
    serviceProviderName?: string // 服务商名称
    totalAmount?: number // 账单总金额
    settlementRatio?: number // 结算比例
    paymentAmount?: number // 付款金额
    status?: number // 状态
    creator?: string // 创建人
    createTime?: string
    updater?: string
    updateTime?: string
    gmtCreate?: string // 缴费单创建时间
    startDate?: string // 需求开始时间(冗余字段)
    endDate?: string // 需求结束时间(冗余字段)
    operatorCode?: string // 会议经办人工号
    operatorName?: string // 会议经办人姓名
    amount?: number // 账单金额
    feeRate?: number // 服务费率
    receiveAmount?: number // 收款金额
}
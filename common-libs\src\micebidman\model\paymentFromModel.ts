import { IPageRequest } from "../../basic";

export class IPayment<PERSON>romFilter extends IPageRequest {
    begin?:string
    end?:string
    startTime?:string
    endTime?:string
    serviceProviderName?:string
    status?:number
}


export class IPaymentFrom {
    id?: number | null
    paymentCode?: string // 付款单号
    serviceProviderName?: string // 服务商名称
    totalAmount?: number // 账单总金额
    settlementRatio?: number // 结算比例
    paymentAmount?: number // 付款金额
    status?: number // 状态
    creator?: string // 创建人
    createTime?: string
    updater?: string
    updateTime?: string
    gmtCreate?: string // 缴费单创建时间
}
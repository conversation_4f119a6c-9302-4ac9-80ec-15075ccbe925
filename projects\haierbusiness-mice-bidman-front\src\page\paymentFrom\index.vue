<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal,
  message,
  TableProps,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';
import {
  IPaymentFromFilter,
  IPaymentFrom
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
import { PaymentFromStatusEnum, PaymentFromStatusMap, PaymentFromStatusTagColorMap } from '@haierbusiness-front/common-libs'
import { flatMap } from 'lodash';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // PaymentOrderlist({
  //   pageNum:1,
  //   pageSize:10
  // })
})

const columns: ColumnType[] = [
  {
    title: '付款单号',
    dataIndex: 'paymentCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'serviceProviderName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRatio',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '缴费单创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IPaymentFromFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(paymentFromApi.getBillList);


const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = undefined
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


// 删除
const { handleDelete } = useDelete(paymentFromApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
}))

const View = ref(false)
//查看
const handleView = ()=>{
  View.value = false
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.startTime = undefined
    searchParam.value.endTime = undefined
  }
});

// 上传付款凭证相关
const uploadModalVisible = ref(false)
const uploadLoading = ref(false)
const currentUploadRecord = ref<any>(null)
const activeKey = ref('1');
const fileList = ref<any[]>([])
const ReasonsRejection = ref()
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 打开上传付款凭证弹窗
const openUploadModal = (record: any) => {
  View.value = true
  currentUploadRecord.value = record
  uploadModalVisible.value = true
  fileList.value = []
}

// 关闭上传弹窗
const closeUploadModal = () => {
  uploadModalVisible.value = false
  currentUploadRecord.value = null
  fileList.value = []
}
//发票
const invoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'name',
  },
  {
    title: '发票日期',
    dataIndex: 'age',
  },
  {
    title: '发票金额',
    dataIndex: '',
    customRender: ({ text }) => text != null ? `${text}元` : '',
  },
];

//上传付款单
const PaymentOrderVisible = ref(false)
//选择的结算单
const settlementList = ref()
const meetingDate = ref<[Dayjs, Dayjs]>();
watch(meetingDate, (newVal) => {
  if (newVal) {
    meetingDate.value = [
      dayjs(newVal[0]).startOf('day'),
      dayjs(newVal[1]).endOf('day')
    ];
  } else {
    meetingDate.value = undefined;
  }
}, { deep: true });

const {
  data: PaymentOrderData,
  run: PaymentOrderlist,
  loading: paymentOrderLoading,
} = usePagination(paymentFromApi.getBillList);

const handlePaymentOrder = () => {

}
const rowSelection: TableProps['rowSelection'] = {
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    settlementList.value = selectedRows
  },
};
//订单
const PaymentOrderColumns: ColumnType<DataType>[] = [
  {
    title: '需求开始时间',
    dataIndex: 'startDate',
    width: '120px',
    align: 'center',
  },
  {
    title: '需求结束时间', 
    dataIndex: 'endDate',
    width: '120px',
    align: 'center',
  },
  {
    title: '会议经办人工号',
    dataIndex: 'operatorCode',
    width: '120px',
    align: 'center',
  },
  {
    title: '会议经办人姓名',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => text != null ? `${text}元` : '',
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => text != null ? `${text}%` : '',
  },
  {
    title: '收款金额',
    dataIndex: 'receiveAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => text != null ? `${text}元` : '',
  },
];
// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      console.log('文件上传成功:', options.file);

      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证');
    return;
  }

  // 这里可以调用API提交付款凭证
  console.log('提交付款凭证:', {
    recordId: currentUploadRecord.value.id,
    files: fileList.value
  });

  message.success('付款凭证上传成功');
  closeUploadModal();
  // 刷新列表
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  });
};

// 生成付款单
const generatePaymentOrder = () => {
  PaymentOrderVisible.value = true
  // // 这里可以调用API生成付款单
  // console.log('生成付款单');
  // message.success('付款单生成成功');
  // // 刷新列表
  // listApiRun({
  //   ...searchParam.value,
  //   pageNum: data.value?.pageNum,
  //   pageSize: data.value?.pageSize,
  // });
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <label for="createTime">会议创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="meetingDate" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.serviceProviderName" placeholder="请输入服务商名称" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" placeholder="请选择状态" allow-clear style="width: 100%">
              <h-select-option :value="PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.CONFIRM_PAYMENT">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.CONFIRM_PAYMENT] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.COMPLETED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="generatePaymentOrder">
              <PlusOutlined />生成付款单
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <h-tag :color="PaymentFromStatusTagColorMap[record.status as keyof typeof PaymentFromStatusTagColorMap]">
                {{ PaymentFromStatusMap[record.status as keyof typeof PaymentFromStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" size="small" @click="handleView">查看</h-button>
              <h-button type="link" size="small" @click="handleDelete(record.id)">删除</h-button>
              <h-button type="link" size="small" @click="openUploadModal(record)">上传付款凭证</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 生成付款单弹窗 -->
    <Modal v-model:open="PaymentOrderVisible" title="上传付款凭证" :footer="null" @cancel="closeUploadModal" width="60%">
      <div>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="4" style="text-align: right;padding-right: 10px;">
            <label for="createTime">缴费单创建时间：</label>
          </h-col>
          <h-col :span="6">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="4" style="text-align: right;">
            <h-button type="primary" @click="handlePaymentOrder()">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <a-table :row-selection="rowSelection" :columns="PaymentOrderColumns" :data-source="PaymentOrderData"
          :loading="paymentOrderLoading" style="margin-top: 15px;">
          <template #bodyCell="{ column, text }">

          </template>
        </a-table>
        <div style="text-align: right; margin-top: 20px;">
          <h-button style="margin-right: 10px;" @click="closeUploadModal">取消</h-button>
          <h-button type="primary" @click="submitUpload" :loading="uploadLoading">提交</h-button>
        </div>
      </div>
    </Modal>

    <!-- 上传付款凭证弹窗 -->
    <Modal v-model:open="uploadModalVisible" title="上传付款凭证" :footer="null" @cancel="closeUploadModal">
      <div style="padding: 20px 0;">
        <div style="margin-bottom: 16px;">
          <strong>付款单号：</strong>{{ currentUploadRecord?.paymentCode }}
        </div>
        <div style="margin-bottom: 16px;">
          <strong>服务商名称：</strong>{{ currentUploadRecord?.serviceProviderName }}
        </div>
        <div style="margin-bottom: 16px;">
          <strong>付款金额：</strong>{{ currentUploadRecord?.paymentAmount }}
        </div>
        <div style="margin-bottom: 16px;">
          <label>付款凭证：</label>
          <h-upload v-model:fileList="fileList" :custom-request="uploadRequest" :multiple="true" :max-count="5"
            @remove="handleFileRemove" accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
            :show-upload-list="true">
            <h-button :loading="uploadLoading">
              <UploadOutlined />
              上传文件
            </h-button>
          </h-upload>
        </div>
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="订单">
            <a-table :columns="PaymentOrderColumns" :data-source="PaymentOrderData?.records || []" :loading="paymentOrderLoading"
              style="margin-top: 15px;">
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票" force-render>
            <a-table :columns="invoiceColumns" :data-source="[]" :loading="paymentOrderLoading"
              style="margin-top: 15px;">
            </a-table>
          </a-tab-pane>
        </a-tabs>
        <div style="margin-bottom: 16px;display: flex;" v-if="View">
          <label>付款凭证：</label>
          <h-upload v-model:fileList="fileList" :custom-request="uploadRequest" :multiple="true" :max-count="5"
            @remove="handleFileRemove" accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
            :show-upload-list="true">
            <h-button :loading="uploadLoading">
              <UploadOutlined />
              上传文件
            </h-button>
          </h-upload>
        </div>
        <div style="margin-bottom: 16px;display: flex;" v-if="View">
          <label>驳回原因：</label>
          <a-textarea v-model:value="ReasonsRejection" show-count :maxlength="200" />
        </div>
        <div style="text-align: right; margin-top: 20px;">
          <h-button style="margin-right: 10px;" @click="closeUploadModal">取消</h-button>
          <h-button type="primary" @click="submitUpload" :loading="uploadLoading">提交</h-button>
        </div>
      </div>
    </Modal>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
